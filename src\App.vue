<template>
  <div class="page-container">
    <el-container class="main-container">
      <el-main>
        <el-card class="app-container">
          <template #header>
            <h1 class="header-title">Bibliographic Items Tagging Tool</h1>
            <el-text>An AI-driven tool for tagging bibliographic items using tags from a tag pool. (e.g.IsisCB's tag pool,
              etc) (Ver 0.2)</el-text>
          </template>

          <!-- Model Selection Dropdown -->
          <!-- Floating button for advanced settings -->
          <div class="floating-button">
            <div><el-button type="primary" circle @click="drawerVisible = true" :icon="Setting" /></div>
          </div>

          <SettingsDrawer
            v-model:drawerVisible="drawerVisible"
            :screenIsPortrait="screenIsPortrait"
            v-model:selectedModel="selectedModel"
            :modelOptions="modelOptions"
            v-model:apiUrl="apiUrl"
            v-model:apiAllTagsUrl="apiAllTagsUrl"
          />

          <DataImporter
            @itemsUpdated="handleItemsUpdated"
          />
          <ProcessingControls
            :biblioItems="biblioItems"
            :isCSVLoading="isCSVLoading"
            :isLoading="isLoading"
            :elapsedCSVTime="elapsedCSVTime"
            :elapsedTime="elapsedTime"
            v-model:batchSize="batchSize"
            :suggestedBatchSize="suggestedBatchSize"
            @submitBiblioItems="submitBiblioItems"
            @clearAllItems="clearAllItems"
          />

          <ResultsDisplay
            :results="results"
            :allIndexedBiblioItems="allIndexedBiblioItems"
            :screenIsPortrait="screenIsPortrait"
            :tagNames="tagNames"
            :allTagCandidates="allTagCandidates"
            :apiAllTagsUrl="apiAllTagsUrl"
            :isFetchingTags="isFetchingTags"
            :hasLoadedTags="hasLoadedTags"
            @tagsUpdated="handleTagsUpdated"
          />

          <ExportControls
            :results="results"
            v-model:newTagMarkEnabled="newTagMarkEnabled"
            v-model:newTagMarkPosition="newTagMarkPosition"
            v-model:newTagMark="newTagMark"
            v-model:metadataSuffixEnabled="metadataSuffixEnabled"
            v-model:selectedMetadataFields="selectedMetadataFields"
            :availableMetadataFields="availableMetadataFields"
            v-model:customSuffixEnabled="customSuffixEnabled"
            v-model:customSuffix="customSuffix"
            v-model:processedTagEnabled="processedTagEnabled"
            v-model:processedTag="processedTag"
            v-model:showExportDialog="showExportDialog"
            :screenIsPortrait="screenIsPortrait"
            v-model:exportOptions="exportOptions"
            v-model:selectAll="selectAll"
            v-model:exportFields="exportFields"
            :availableFields="availableFields"
            v-model:includeConceptTags="includeConceptTags"
            v-model:includePersonOrgTags="includePersonOrgTags"
            v-model:includeTimePlaceTags="includeTimePlaceTags"
            :currentSource="currentSource"
            @downloadCSV="downloadCSV"
            @openExportDialog="openExportDialog"
            @selectAll="handleSelectAll"
            @export="handleExport"
            @saveTagsToZotero="saveTagsToZotero"
          />
        </el-card>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, toRaw, isReactive } from 'vue'
import axios from 'axios'
import { debounce } from 'lodash'
import {
  ElContainer,
  ElMain,
  ElCard,
  ElButton,
  ElText,
  ElMessage,
  ElMessageBox
} from 'element-plus'
import { ZoomIn, ZoomOut, Setting } from '@element-plus/icons-vue'

// Import new components
import SettingsDrawer from './components/SettingsDrawer.vue'
import DataImporter from './components/DataImporter.vue'
import ProcessingControls from './components/ProcessingControls.vue'
import ResultsDisplay from './components/ResultsDisplay.vue'
import ExportControls from './components/ExportControls.vue'

const drawerVisible = ref(false)
const screenIsPortrait = ref(window.innerHeight > window.innerWidth);
const drawerDirection = computed(() => {
  return screenIsPortrait.value ? 'btt' : 'rtl'; // 'btt' = bottom, 'rtl' = right
});

const biblioItems = ref([]) // This is for storing the original items imported from data sources
const allIndexedBiblioItems = ref([]) // This is the indexed version of biblioItems. It will be populated when calling submitBiblioItems function.
const results = ref([]) // This is for storing the results from the API.
const isLoading = ref(false)

// Tag state for export functionality (received from ResultsDisplay)
const deselectedTags = ref(new Set())
const newTags = ref(new Map())

const apiUrl = ref('http://127.0.0.1:5011/api/generate_tags')
const apiAllTagsUrl = ref('http://127.0.0.1:5011/api/tags/all')
const batchSize = ref(null)
const suggestedBatchSize = computed(() => {
  const length = biblioItems.value.length;
  if (length === 0) {
    return 1; // Default when there are no items
  } else if (length <= 5) {
    return length; // Process all items if there are 5 or fewer
  } else if (length <= 50) {
    return Math.ceil(length / 5); // Split into ~5 batches for datasets between 6 and 50
  } else if (length <= 200) {
    return Math.ceil(length / 10); // Split into ~10 batches for datasets between 51 and 200
  } else {
    return Math.ceil(length / 20); // Split into ~20 batches for datasets larger than 200
  }
});


const isCSVLoading = ref(false)
const elapsedTime = ref(0)
const elapsedCSVTime = ref(0)
let timerInterval = null
let csvTimerInterval = null

// Add model selection related refs
const selectedModel = ref('gpt-4o-mini')
const modelOptions = ref([
  { value: 'gpt-4o-mini', label: 'gpt-4o-mini (3~4s/item)' },
  { value: 'pytextrank', label: 'pytextrank (very fast)' }
  // Add more model options here
])

// TAG CONFIGURATION
const allTagCandidates = ref([]) // Store all tags from the curated tag pool
const tagNames = computed(() => allTagCandidates.value.map(tag => tag.name))
const isFetchingTags = ref(false)
const hasLoadedTags = ref(false) // Track if tags have been loaded
// Configuration for decorating tags after reviewing
const newTagMarkEnabled = ref(true)
const newTagMarkPosition = ref('suffix')
const newTagMark = ref('[NEW]')
const metadataSuffixEnabled = ref(true)
const selectedMetadataFields = ref(['record_id'])
const customSuffixEnabled = ref(false)
const customSuffix = ref('[IsisCBtag]')
const processedTagEnabled = ref(true)
const processedTag = ref('processed-by-tagger')
// Available metadata fields (excluding 'name')
const availableMetadataFields = ref([]);
// Function to update availableMetadataFields
const updateMetadataFields = () => {
  if (!allTagCandidates.value || allTagCandidates.value.length === 0) {
    availableMetadataFields.value = [{ label: 'N/A', value: 'N/A' }]; // Default value
    return;
  }

  const firstObject = allTagCandidates.value[0];
  const fields = Object.keys(toRaw(firstObject || {})); // Guard against null/undefined
  const filteredFields = fields.filter(field => field !== 'name');

  availableMetadataFields.value = filteredFields.map(field => ({
    label: field,
    value: field
  }));
};

watch(allTagCandidates, updateMetadataFields, { immediate: true });
// Function to generate preview suffix
const getPreviewSuffix = () => {
  let suffix = ''

  if (metadataSuffixEnabled.value && selectedMetadataFields.value.length > 0) {
    suffix += `[${selectedMetadataFields.value.join('|')}]` // Example preview
  }

  return suffix
}

// Function to update screen orientation
const updateScreenOrientation = () => {
  screenIsPortrait.value = window.innerHeight > window.innerWidth;
};

// Add event listener to handle screen resizing
onMounted(() => {
  window.addEventListener('resize', updateScreenOrientation);
});

// Clean up the event listener when the component is unmounted
onUnmounted(() => {
  window.removeEventListener('resize', updateScreenOrientation);
});

// Data source tracking (simplified)
const currentSource = ref('') // To track data source

// Zotero configuration (received from DataImporter when needed)
const zoteroConfig = ref(null)
const zoteroBaseUrl = ref('')

// Handle items updated from DataImporter
const handleItemsUpdated = (items, removedItems, source, zoteroData = null) => {
  biblioItems.value = items
  currentSource.value = source

  // Store Zotero configuration if provided
  if (source === 'Zotero' && zoteroData) {
    zoteroConfig.value = zoteroData.config
    zoteroBaseUrl.value = zoteroData.baseUrl
  } else if (source !== 'Zotero') {
    // Clear Zotero config when switching to non-Zotero sources
    zoteroConfig.value = null
    zoteroBaseUrl.value = ''
  }

  // Clear any existing results when new items are loaded
  results.value = []
  allIndexedBiblioItems.value = []
}

// Handle tags updated from ResultsDisplay
const handleTagsUpdated = (tagData) => {
  deselectedTags.value = tagData.deselectedTags
  newTags.value = tagData.newTags
}



const startTimer = (isCSV = false) => {
  if (isCSV) {
    elapsedCSVTime.value = 0
    csvTimerInterval = setInterval(() => {
      elapsedCSVTime.value++
    }, 1000)
  } else {
    elapsedTime.value = 0
    timerInterval = setInterval(() => {
      elapsedTime.value++
    }, 1000)
  }
}

const stopTimer = (isCSV = false) => {
  if (isCSV) {
    if (csvTimerInterval) {
      clearInterval(csvTimerInterval)
      csvTimerInterval = null
    }
  } else {
    if (timerInterval) {
      clearInterval(timerInterval)
      timerInterval = null
    }
  }
}

const submitBiblioItems = async () => {
  try {
    isCSVLoading.value = true
    startTimer(true)

    // Clear previous results
    results.value = []

    // VERY IMPORTANT: Index all items. After this step, allIndexedBiblioItems should be used instead of biblioItems.
    allIndexedBiblioItems.value = biblioItems.value.map((item, idx) => ({
      ...item,
      index: idx,
    }))

    // Use the user's chosen batch size (or suggested if not set)
    const size = batchSize.value || suggestedBatchSize.value

    // Process items in batches
    for (let i = 0; i < allIndexedBiblioItems.value.length; i += size) {
      const batch = allIndexedBiblioItems.value.slice(i, i + size)
      const response = await axios.post(`${apiUrl.value}`, {
        model: selectedModel.value,
        items: batch.map(article => ({
          key: article.key,
          title: article.title,
          abstract: article.abstract,
          index: article.index
        }))
      })

      results.value.push(...response.data)
      ElMessage.success(`Batch ${Math.floor(i / size) + 1} tagged successfully!`)
      await fetchAllTags()
    }
  } catch (error) {
    console.error('Error batch-tagging articles:', error)
    ElMessage.error('An error occurred while batch-tagging articles.')
  } finally {
    stopTimer(true)
    isCSVLoading.value = false
  }
}

const clearAllItems = () => {
  ElMessageBox.confirm('This will remove all items. Continue?', 'Warning', {
    confirmButtonText: 'Yes',
    cancelButtonText: 'No',
    type: 'warning',
  }).then(() => {
    biblioItems.value = []
    allIndexedBiblioItems.value = []
    results.value = []
    currentSource.value = ''
    batchSize.value = null
    deselectedTags.value = new Set()
    newTags.value = new Map()
    // allTagCandidates.value = []
    ElMessage({
      type: 'success',
      message: 'All items have been cleared',
      duration: 2000
    })
  }).catch((error) => {
    console.error('Error in clearAllItems:', error)
  })
}






// Helper function for export functionality
const isTagDeselected = (resultIndex, tag) => {
  return deselectedTags.value.has(`${resultIndex}-${tag}`)
}



// Function to fetch all tags
const fetchAllTags = async () => {
  if (hasLoadedTags.value) return // Don't fetch if already loaded

  try {
    isFetchingTags.value = true
    const response = await axios.get(`${apiAllTagsUrl.value}`)
    allTagCandidates.value = response.data
    hasLoadedTags.value = true
    ElMessage.success('Tag pool loaded successfully.')
  } catch (error) {
    console.error('Error fetching tags:', error)
    ElMessage.error('Failed to fetch tags')
    allTagCandidates.value = []
  } finally {
    isFetchingTags.value = false
  }
}

// Watch for changes to metadataSuffixEnabled
watch(metadataSuffixEnabled, async (newValue) => {
  if (newValue === true) {
    await fetchAllTags();
  }
});



// Download CSV
const exportFields = ref([])
const includeConceptTags = ref(true)
const includePersonOrgTags = ref(true)
const includeTimePlaceTags = ref(true)
const showExportDialog = ref(false)
const selectAll = ref(false)

const exportOptions = ref({
  fieldDelimiter: ',',
  tagDelimiter: ';',
  valueMarkers: '"'
})

const availableFields = computed(() => {
  if (allIndexedBiblioItems.value.length === 0) return []

  return Object.keys(allIndexedBiblioItems.value[0])
    .filter(key => key !== 'index')
    .filter(key => allIndexedBiblioItems.value.some(item => {
      const value = item[key]
      return value !== null && value !== undefined && String(value).trim() !== ''
    }))
})

const openExportDialog = () => {
  exportFields.value = [...availableFields.value]
  selectAll.value = true
  showExportDialog.value = true
}

const handleSelectAll = (val) => {
  if (val) {
    exportFields.value = [...availableFields.value]
  } else {
    exportFields.value = []
  }
}

const handleExport = () => {
  downloadCSV()
  showExportDialog.value = false
}

const downloadCSV = () => {
  // If the user directly clicked the export button, the exportFields array would be empty. So it should be set to all available fields by default.
  if (exportFields.value.length === 0) {
    exportFields.value = [...availableFields.value];
  }

  const { fieldDelimiter, tagDelimiter, valueMarkers } = exportOptions.value;
  const startMarker = valueMarkers || '';
  const endMarker = valueMarkers || startMarker;

  const csvData = results.value.map(result => {
    const item = allIndexedBiblioItems.value.find(item => item.index === result.index);
    const row = exportFields.value.map(field => {
      // Replace undefined or null field values with an empty string
      let fieldValue = item[field] != null ? item[field] : '';

      // If fieldValue is not a string and is an object or array (e.g. a list of several authors), expand it
      if (isReactive(fieldValue) || Array.isArray(fieldValue)) {
        const rawFieldValue = toRaw(fieldValue); // Convert to raw object/array

        if (Array.isArray(rawFieldValue)) {
          // Check for array of objects with firstName or lastName
          fieldValue = rawFieldValue
            .map(entry => {
              if (entry.lastName || entry.firstName) {
                // Use empty string if one of the values is missing
                const lastName = entry.lastName || '';
                const firstName = entry.firstName || '';
                return `${lastName},${firstName}`.trim();
              }
              return ''; // Skip invalid entries
            })
            .filter(Boolean) // Remove empty strings
            .join(tagDelimiter); // Join with tagDelimiter
        }
      } else if (typeof fieldValue === 'object') {
        // Handle plain objects by joining values with tagDelimiter
        fieldValue = Object.values(fieldValue).join(tagDelimiter);
      } else if (typeof fieldValue !== 'string') {
        // Convert other non-string values to strings
        fieldValue = String(fieldValue);
      }
      return `${startMarker}${fieldValue}${endMarker}`;
    });

    // Decorate active matched tags (those matched tags that are still active (not de-selected) after being edited by the user)
    const decoratedActiveMatchedTags = result.tags.matched_tags
      .filter(tag => !isTagDeselected(result.index, tag))
      .map(tagText => {
        const matchingTag = allTagCandidates.value.find(t => t.name === tagText);

        // Add metadata suffix if metadataSuffixEnabled is true and tag exists in allTagCandidates
        let decoratedTagText = '';
        if (metadataSuffixEnabled.value && matchingTag) {
          const metadataSuffix = selectedMetadataFields.value
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|');
          if (metadataSuffix) {
            decoratedTagText = `${tagText} [${metadataSuffix}]`;
          }
        }

        // Add custom suffix to all tags if enabled
        if (customSuffixEnabled.value) {
          decoratedTagText = `${decoratedTagText}${customSuffix.value}`;
        }

        return decoratedTagText;
      });

    // Process newly added customized tags (tags added by user and not in the tag pool)
    const decoratedNewTags = Array.from(newTags.value.get(result.index) || [])
      .map(tag => {
        let decoratedNewTagText = tag.text;
        if (newTagMarkEnabled.value && !tag.isMatched) {
          if (newTagMarkPosition.value === 'prefix') {
            decoratedNewTagText = `${newTagMark.value}${tag.text}`;
          } else if (newTagMarkPosition.value === 'suffix') {
            decoratedNewTagText = `${tag.text}${newTagMark.value}`;
          }
        }

        const matchingTag = allTagCandidates.value.find(t => t.name === tag.text);

        // Add metadata suffix if enabled and tag exists in allTagCandidates
        if (metadataSuffixEnabled.value && matchingTag) {
          const metadataSuffix = selectedMetadataFields.value
            .map(field => matchingTag[field] != null ? matchingTag[field] : '')
            .filter(Boolean)
            .join('|');
          if (metadataSuffix) {
            decoratedNewTagText = `${decoratedNewTagText} [${metadataSuffix}]`;
          }
        }

        // Add custom suffix if enabled
        if (customSuffixEnabled.value) {
          decoratedNewTagText = `${decoratedNewTagText}${customSuffix.value}`;
        }

        return decoratedNewTagText;
      });

    // Combine all tags and add processed tag if enabled
    const allDecoratedTags = [...new Set([...decoratedActiveMatchedTags, ...decoratedNewTags])];
    if (processedTagEnabled.value && processedTag.value) {
      allDecoratedTags.push(processedTag.value);
    }

    // Add combined tags to row
    row.push(`${startMarker}${allDecoratedTags.join(tagDelimiter)}${endMarker}`);

    // Add other tag categories if selected
    if (includeConceptTags.value) row.push(`${startMarker}${result.tags.concept_tags.join(tagDelimiter)}${endMarker}`);
    if (includePersonOrgTags.value) row.push(`${startMarker}${result.tags.person_org_tags.join(tagDelimiter)}${endMarker}`);
    if (includeTimePlaceTags.value) row.push(`${startMarker}${result.tags.time_place_tags.join(tagDelimiter)}${endMarker}`);

    return row.join(fieldDelimiter);
  });

  // Create header row
  const headerRow = [
    ...exportFields.value,
    'Matched Tags',
    ...(includeConceptTags.value ? ['Concept Tags'] : []),
    ...(includePersonOrgTags.value ? ['Person/Org Tags'] : []),
    ...(includeTimePlaceTags.value ? ['Time/Place Tags'] : [])
  ].map(header => `${startMarker}${header}${endMarker}`);

  // Add header row to csvData
  csvData.unshift(headerRow.join(fieldDelimiter));

  const csv = csvData.join('\n');
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = 'tagged_items.csv';
  link.click();
};

// Save matched tags to Zotero
const saveTagsToZotero = async () => {
  if (currentSource.value !== 'Zotero' || !zoteroConfig.value) {
    ElMessage.error('Can only save to Zotero for items imported from Zotero')
    return
  }

  try {
    const { libraryId, apiKey } = zoteroConfig.value
    let successCount = 0
    let failureCount = 0

    const updatePromises = results.value.map(async (result) => {
      const item = allIndexedBiblioItems.value.find(item => item.index === result.index)
      if (!item?.key || !item?.version) return null

      try {
        // Fetch the current item to get its existing tags
        const currentItemResponse = await axios.get(
          `${zoteroBaseUrl.value}/items/${item.key}`,
          {
            headers: {
              'Zotero-API-Version': '3',
              'Authorization': `Bearer ${apiKey}`
            }
          }
        )

        // Get existing tags, excluding the tag used for import and any previously added suffixes
        const existingTags = currentItemResponse.data.data.tags
          .filter(tagObj => {
            const tag = tagObj.tag
            return tag !== zoteroConfig.value.tag &&
              (!customSuffixEnabled.value || !tag.endsWith(customSuffix.value)) &&
              (!processedTagEnabled.value || tag !== processedTag.value)
          })
          .map(tagObj => tagObj.tag)

        // Process matched tags with metadata and custom suffixes
        const decoratedActiveMatchedTags = result.tags.matched_tags
          .filter(tag => !isTagDeselected(result.index, tag))
          .map(tagName => {
            let decoratedTag = tagName
            const matchingTag = allTagCandidates.value.find(t => t.name === tagName)

            // Add metadata suffix if enabled and tag exists in allTagCandidates
            if (metadataSuffixEnabled.value && matchingTag) {
              const metadataSuffix = selectedMetadataFields.value
                .map(field => `${matchingTag[field]}`)
                .filter(Boolean)
                .join('|')
              if (metadataSuffix) {
                decoratedTag = `${decoratedTag} [${metadataSuffix}]`
              }
            }

            // Add custom suffix if enabled
            if (customSuffixEnabled.value) {
              decoratedTag = `${decoratedTag}${customSuffix.value}`
            }

            return decoratedTag
          })

        // Process new tags with metadata and custom suffixes
        const decoratedNewTags = Array.from(newTags.value.get(result.index) || [])
          .map(tag => {
            let tagText = tag.text
            // Add new tag mark if enabled
            if (newTagMarkEnabled.value && !tag.isMatched) {
              if (newTagMarkPosition.value === 'prefix') {
                tagText = `${newTagMark.value}${tagText}`
              } else if (newTagMarkPosition.value === 'suffix') {
                tagText = `${tagText}${newTagMark.value}`
              }
            }

            let decoratedTag = tagText
            const matchingTag = allTagCandidates.value.find(t => t.name === tag.text)

            // Add metadata suffix if enabled and tag exists in allTagCandidates
            if (metadataSuffixEnabled.value && matchingTag) {
              const metadataSuffix = selectedMetadataFields.value
                .map(field => `${matchingTag[field]}`)
                .filter(Boolean)
                .join('|')
              if (metadataSuffix) {
                decoratedTag = `${decoratedTag} [${metadataSuffix}]`
              }
            }

            // Add custom suffix if enabled
            if (customSuffixEnabled.value) {
              decoratedTag = `${decoratedTag}${customSuffix.value}`
            }

            return decoratedTag
          })

        // Combine all tags
        const allDecoratedTags = [...new Set([
          ...existingTags,
          ...decoratedActiveMatchedTags,
          ...decoratedNewTags
        ])]

        // Format tags for Zotero API
        const tagsToUpdate = allDecoratedTags.map(tag => ({ tag }))

        // // Add back the import tag
        // if (zoteroConfig.value.tag) {
        //   tagsToUpdate.push({ tag: zoteroConfig.value.tag })
        // }

        // Add the processed tag if enabled
        if (processedTagEnabled.value && processedTag.value) {
          tagsToUpdate.push({ tag: processedTag.value })
        }

        // Update the item with combined tags
        await axios.patch(
          `${zoteroBaseUrl.value}/items/${item.key}`,
          {
            tags: tagsToUpdate
          },
          {
            headers: {
              'Zotero-API-Version': '3',
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json',
              'If-Unmodified-Since-Version': currentItemResponse.data.version
            }
          }
        )
        successCount++
      } catch (error) {
        console.error(`Error updating item ${item.key}:`, error)
        failureCount++
        throw error
      }
    })

    await Promise.all(updatePromises)

    if (failureCount === 0) {
      ElMessage.success(`Successfully saved tags to Zotero for all ${successCount} items`)
    } else {
      ElMessage.warning(`Saved tags for ${successCount} items, failed for ${failureCount} items`)
    }
  } catch (error) {
    console.error('Error saving tags to Zotero:', error)
    let errorMessage = 'Failed to save tags to Zotero'

    if (error.response) {
      switch (error.response.status) {
        case 403:
          errorMessage = 'Invalid API key or insufficient permissions'
          break
        case 404:
          errorMessage = 'Library or item not found'
          break
        case 412:
          errorMessage = 'Item was modified since last retrieval. Please refresh and try again'
          break
        case 429:
          errorMessage = 'Too many requests. Please try again later'
          break
        default:
          errorMessage = error.response.data?.message || errorMessage
      }
    }

    ElMessage.error(errorMessage)
  }
}

// Helper functions for components
const getAbstractByIndex = (index) => {
  const item = allIndexedBiblioItems.value.find(item => item.index === index)
  return item ? item.abstract : 'Abstract not found'
}
</script>

<style scoped>
.el-row {
  margin-bottom: 20px;
}

.el-divider--nowrap {
  text-align: center;
  white-space: nowrap;
  padding: 0;
  width: 100%;
  max-width: 100%;
}

.el-input-delimiter {
  width: 2rem;
}

.el-pagination {
  flex-wrap: wrap;
  gap: 5px;
}

.mark-position-select {
  width: 4rem;
  height: 1.8rem;
}

.center-content {
  display: flex;
  justify-content: center;
  /* Horizontally center child elements */
  align-items: center;
  /* Vertically center child elements */
  gap: 5px;
  /* Add spacing between child elements */
  flex-wrap: wrap;
  /* Ensure responsiveness if elements exceed available space */
  text-align: center;
}

.custom-input-number {
  width: 80px;
}

.button-container {
  display: flex;
  justify-content: center;
  /* Ensures the content inside <el-col> is centered */
  align-items: center;
  /* Aligns the button vertically within the container */
}

.page-container {
  min-height: 100vh;
  /* height: 100%; */
  width: 100%;
  display: flex;
  justify-content: center;
}

.main-container {
  width: 100%;
  max-width: 1200px;
  padding: 20px;
}

.app-container {
  width: 100%;
  height: 100%;
  margin: auto auto;
}


.header-title {
  margin: 0;
  font-size: 24px;
  color: var(--el-text-color-primary);
}

.article-form {
  width: 100%;
}

.article-input {
  margin-bottom: 30px;
  padding: 20px;
  border-radius: 8px;
  background-color: var(--el-bg-color);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.form-item {
  margin-bottom: 20px;
}

.title-input,
.abstract-input {
  width: 100%;
}

.divider-title {
  font-size: 18px;
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin: 20px 0;
  justify-content: center;
}

.csv-submit-button {
  margin-top: 20px;
}

.loading-message {
  text-align: center;
  margin: 20px 0;
  padding: 20px;
  background-color: var(--el-color-primary-light-9);
  border-radius: 4px;
  color: var(--el-text-color-primary);
}

.results {
  margin-top: 10px;
}

.wrap-text {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tag-item {
  margin: 0;
}

.el-tag {
  white-space: normal; /* Allow text to wrap */
  word-break: break-word; /* Break long words if necessary */
  min-height: 1.5rem;
  height: fit-content;
  max-width: 100%; /* Ensure the tag doesn't overflow its container */
}


/* For tag matching */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.loading-icon {
  width: 14px;
  height: 14px;
  animation: rotate 1s linear infinite;
}

.tag-input-container {
  position: relative;
  display: inline-block;
}

.tag-suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: flex;
  max-height: 300px;
  background: white;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow-y: auto;
}

.tag-suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.3s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-suggestion-item:hover {
  background-color: var(--el-color-primary-light-9);
}

.loading-indicator {
  padding: 8px 12px;
  color: var(--el-text-color-secondary);
  text-align: center;
}

/* Add new styles for drag and drop */
.draggable-tag {
  cursor: move;
  transition: transform 0.2s;
}

.draggable-tag:hover {
  transform: scale(1.05);
}

.draggable-tag:active {
  cursor: grabbing;
}

.droppable-area {
  min-height: 40px;
  padding: 8px;
  border: 2px dashed transparent;
  border-radius: 4px;
  transition: all 0.3s;
}

.droppable-area:hover {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

/* New styles for CSV preview */
.csv-section {
  margin: 20px 0;
  padding: 20px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 8px;
}

.csv-preview {
  margin-top: 20px;
}

.preview-item {
  padding: 8px;
  margin: 4px 0;
  background-color: var(--el-bg-color);
  border-radius: 4px;
}

.preview-title {
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.more-items {
  margin-top: 8px;
  color: var(--el-text-color-secondary);
  font-style: italic;
}

.download-section {
  margin-top: 20px;
  text-align: center;
}

.clickable-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.deselected-tag {
  opacity: 0.5;
  text-decoration: line-through;
}

.new-tag {
  background-color: var(--el-color-success-light-9);
  border-color: var(--el-color-success);
  color: var(--el-color-success);
}

.tag-input {
  width: 280px;
  max-width: 100%;
  margin-left: 8px;
}

.floating-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 10px;
}

.floating-button .el-button {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* Optional: Add hover effect for clickable tags */
.clickable-tag:hover {
  transform: scale(1.05);
}

.truncate-title {
  /* display: inline-block; */
  max-width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.el-form-item__label) {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
}

:deep(.el-card__header) {
  text-align: center;
  padding: 10px;
  background-color: var(--el-color-primary-light-9);
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
  /* font-weight: bold; */
}

:deep(.el-descriptions__label) {
  font-weight: bold;
  width: 200px;
  padding-right: 16px;
}

:deep(.el-textarea__inner) {
  min-height: 120px !important;
}

:deep(.el-main) {
  padding: 0;
}

:deep(.el-descriptions__table) {
  width: 100%; /* Ensure the table takes full width of its container */
  max-width: 100%; /* Prevent overflow */
  table-layout: fixed; /* Ensure the table respects column widths */
}

:deep(.el-descriptions__cell) {
  word-break: break-word; /* Break long words to prevent overflow */
  white-space: normal; /* Allow text to wrap */
}


/* zotero-related */
.zotero-form {
  padding: 30px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 0px;
  margin-bottom: 0px;
}

.source-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.fetch-button {
  width: 100%;
  margin-top: 16px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
}

:deep(.el-form-item__label) {
  font-weight: bold;
}
</style>