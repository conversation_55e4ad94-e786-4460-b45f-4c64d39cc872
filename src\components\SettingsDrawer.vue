<template>
  <el-drawer title="Advanced settings" v-model="drawerVisible" :direction="drawerDirection">
    <el-divider class="el-divider--nowrap">Select Tag Generator</el-divider>
    <el-select v-model="selectedModel" placeholder="Select Tagger">
      <el-option v-for="item in modelOptions" :key="item.value" :label="item.label" :value="item.value">
      </el-option>
    </el-select>
    <el-divider class="el-divider--nowrap">Customize API's URL</el-divider>
    <el-input v-model="apiUrl" placeholder="Enter API URL"></el-input>
    <el-input v-model="apiAllTagsUrl" placeholder="Enter API URL for getting the tag pool."></el-input>
  </el-drawer>
</template>

<script setup>
import { computed } from 'vue'
import { ElDrawer, ElDivider, ElSelect, ElOption, ElInput } from 'element-plus'

// Props
const props = defineProps({
  drawerVisible: {
    type: <PERSON>olean,
    required: true
  },
  screenIsPortrait: {
    type: Boolean,
    required: true
  },
  selectedModel: {
    type: String,
    required: true
  },
  modelOptions: {
    type: Array,
    required: true
  },
  apiUrl: {
    type: String,
    required: true
  },
  apiAllTagsUrl: {
    type: String,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:drawerVisible', 'update:selectedModel', 'update:apiUrl', 'update:apiAllTagsUrl'])

// Computed properties
const drawerDirection = computed(() => {
  return props.screenIsPortrait ? 'btt' : 'rtl' // 'btt' = bottom, 'rtl' = right
})

// Reactive properties with v-model support
const drawerVisible = computed({
  get: () => props.drawerVisible,
  set: (value) => emit('update:drawerVisible', value)
})

const selectedModel = computed({
  get: () => props.selectedModel,
  set: (value) => emit('update:selectedModel', value)
})

const apiUrl = computed({
  get: () => props.apiUrl,
  set: (value) => emit('update:apiUrl', value)
})

const apiAllTagsUrl = computed({
  get: () => props.apiAllTagsUrl,
  set: (value) => emit('update:apiAllTagsUrl', value)
})
</script>

<style scoped>
/* Component-specific styles can be added here if needed */
</style>
