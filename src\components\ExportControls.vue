<template>
  <div>
    <!-- Step 3. Export Result -->
    <el-divider class="el-divider--nowrap">Step 3. Export Result</el-divider>
    <div v-if="results.length" class="download-section">
      <el-row :gutter="20" justify="center" class="center-content">
        <!-- Prefix or suffix for new customized tags -->
        <el-col :span="6" :xs="24">
          <el-checkbox v-model="newTagMarkEnabled">Add <el-select v-model="newTagMarkPosition"
              :disabled="!newTagMarkEnabled" size="small" class="mark-position-select">
              <el-option label="prefix" value="prefix"></el-option>
              <el-option label="suffix" value="suffix"></el-option>
            </el-select>
            to customized tags</el-checkbox>
          <el-input :disabled="!newTagMarkEnabled" v-model="newTagMark" placeholder="Enter prefix">
            <template #prepend>
              <el-tooltip
                content="This prefix will be added to all customized tags (new tags not in the tag pool) in the exported CSV"
                placement="top">
                <el-icon>
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </template>
          </el-input>
          <!-- Metadata suffix section -->
          <el-checkbox v-model="metadataSuffixEnabled">
            Add metadata suffix to matched tags
          </el-checkbox>
          <el-select :disabled="!metadataSuffixEnabled || !(availableMetadataFields.length > 0)"
            v-model="selectedMetadataFields" multiple placeholder="Select metadata fields">
            <el-option v-for="field in availableMetadataFields" :key="field.value" :label="field.label"
              :value="field.value" />
          </el-select>
        </el-col>

        <el-col :span="6" :xs="24">
          <!-- Custom suffix section -->
          <el-checkbox v-model="customSuffixEnabled">
            Add custom suffix
          </el-checkbox>
          <el-input v-model="customSuffix" placeholder="Enter custom suffix" :disabled="!customSuffixEnabled"
            class="suffix-input">
            <template #prepend>
              <el-tooltip content="This suffix will be added to all matched tags when saving to Zotero"
                placement="top">
                <el-icon>
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </template>
          </el-input>
          <!-- "Processed by this tool" tag -->
          <el-checkbox v-model="processedTagEnabled">
            Add an extra tag for all processed items
          </el-checkbox>
          <el-input v-model="processedTag" placeholder="Enter marker tag" :disabled="!processedTagEnabled">
            <template #prepend>
              <el-tooltip content="This tag will be added to all items processed by this tool" placement="top">
                <el-icon>
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </template>
          </el-input>
        </el-col>

        <el-col :span="6" :xs="24" class="center-content">
          <!-- Preview section -->
          <div><el-text size="default">Preview of tags to be added: </el-text></div>
          <el-tag v-if="!(metadataSuffixEnabled && selectedMetadataFields.length > 0) && !customSuffixEnabled"
            type="primary">
            matched-tags
          </el-tag>
          <el-tag v-if="!newTagMarkEnabled && !customSuffixEnabled" type="warning">
            customized-tags
          </el-tag>

          <el-tag v-if="metadataSuffixEnabled && selectedMetadataFields.length > 0 && customSuffixEnabled"
            class="preview-tag" type="primary">
            matched-tags{{ getPreviewSuffix() }}{{ customSuffix }}
          </el-tag>
          <el-tag v-if="metadataSuffixEnabled && selectedMetadataFields.length > 0 && !customSuffixEnabled"
            class="preview-tag" type="primary">
            matched-tags{{ getPreviewSuffix() }}
          </el-tag>
          <el-tag v-if="!metadataSuffixEnabled && customSuffixEnabled" type="primary">
            matched-tags{{ customSuffix }}
          </el-tag>

          <el-tag v-if="!newTagMarkEnabled && customSuffixEnabled" type="warning">
            customized-tags{{ customSuffix }}
          </el-tag>
          <el-tag v-if="newTagMarkEnabled && newTagMarkPosition === 'prefix' && customSuffixEnabled"
            type="warning">
            {{ newTagMark }}customized-tags{{ customSuffix }}
          </el-tag>
          <el-tag v-if="newTagMarkEnabled && newTagMarkPosition === 'suffix' && customSuffixEnabled"
            type="warning">
            customized-tags{{ newTagMark }}{{ customSuffix }}
          </el-tag>
          <el-tag v-if="newTagMarkEnabled && newTagMarkPosition === 'prefix' && !customSuffixEnabled"
            type="warning">
            {{ newTagMark }}customized-tags
          </el-tag>
          <el-tag v-if="newTagMarkEnabled && newTagMarkPosition === 'suffix' && !customSuffixEnabled"
            type="warning">
            customized-tags{{ newTagMark }}
          </el-tag>

          <el-tag v-if="processedTagEnabled" type="info">
            {{ processedTag }}
          </el-tag>
        </el-col>
      </el-row>

      <el-row :gutter="10" justify="center" class="mt-4 center-content">
        <el-col :span="6" :xs="24" class="button-container">
          <el-button type="success" @click="handleDownloadCSV">
            Download To CSV
          </el-button>
          <el-button type="info" @click="handleOpenExportDialog" :disabled="results.length === 0">
            CSV Options
          </el-button>

          <!-- Add this dialog for export options -->
          <el-dialog v-model="showExportDialog" title="CSV Export Options" :width="screenIsPortrait ? '90%' : '50%'">
            <el-form :model="exportOptions">
              <el-divider class="el-divider--nowrap">Fields to export</el-divider>
              <el-checkbox v-model="selectAll" @change="handleSelectAll">Select All</el-checkbox>
              <el-row :gutter="5" justify="left">
                <el-checkbox-group v-model="exportFields">
                  <el-checkbox v-for="field in availableFields" :key="field" :label="field" :value="field">
                    {{ field }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-row>
              <el-divider class="el-divider--nowrap">Include auto tags</el-divider>
              <el-row :gutter="10" justify="center">
                <el-checkbox v-model="includeConceptTags">Concept tags</el-checkbox>
                <el-checkbox v-model="includePersonOrgTags">Person/Organization tags</el-checkbox>
                <el-checkbox v-model="includeTimePlaceTags">Time/Place tags</el-checkbox>
              </el-row>

              <el-divider class="el-divider--nowrap">Delimiter settings</el-divider>
              <el-row :gutter="10" justify="center" class="center-content">
                <el-col :span="6" :xs="24">
                  <el-text>Field delimiter: </el-text>
                  <el-input v-model="exportOptions.fieldDelimiter" class="el-input-delimiter"></el-input>
                </el-col>
                <el-col :span="6" :xs="24">
                  <el-text>Tag delimiter: </el-text>
                  <el-input v-model="exportOptions.tagDelimiter" class="el-input-delimiter"></el-input>
                </el-col>
                <el-col :span="6" :xs="24">
                  <el-text>Field enclosure: </el-text>
                  <el-input v-model="exportOptions.valueMarkers" class="el-input-delimiter"></el-input>
                </el-col>
              </el-row>
            </el-form>
            <template #footer>
              <span class="dialog-footer">
                <el-button @click="showExportDialog = false">Cancel</el-button>
                <el-button type="success" @click="handleExport">Download To CSV</el-button>
              </span>
            </template>
          </el-dialog>
        </el-col>
        <el-col :span="6" :xs="24" class="button-container">
          <el-tooltip
            :disabled="currentSource === 'Zotero'"
            content="Please import items from Zotero to enable this option."
            placement="top"
          >
            <el-button
              type="success"
              @click="handleSaveTagsToZotero"
              :disabled="currentSource !== 'Zotero'"
            >
              Save Matched Tags to Zotero
            </el-button>
          </el-tooltip>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import {
  ElDivider,
  ElRow,
  ElCol,
  ElCheckbox,
  ElSelect,
  ElOption,
  ElInput,
  ElTooltip,
  ElIcon,
  ElText,
  ElTag,
  ElButton,
  ElDialog,
  ElForm,
  ElCheckboxGroup
} from 'element-plus'
import { InfoFilled } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  results: {
    type: Array,
    required: true
  },
  newTagMarkEnabled: {
    type: Boolean,
    required: true
  },
  newTagMarkPosition: {
    type: String,
    required: true
  },
  newTagMark: {
    type: String,
    required: true
  },
  metadataSuffixEnabled: {
    type: Boolean,
    required: true
  },
  selectedMetadataFields: {
    type: Array,
    required: true
  },
  availableMetadataFields: {
    type: Array,
    required: true
  },
  customSuffixEnabled: {
    type: Boolean,
    required: true
  },
  customSuffix: {
    type: String,
    required: true
  },
  processedTagEnabled: {
    type: Boolean,
    required: true
  },
  processedTag: {
    type: String,
    required: true
  },
  showExportDialog: {
    type: Boolean,
    required: true
  },
  screenIsPortrait: {
    type: Boolean,
    required: true
  },
  exportOptions: {
    type: Object,
    required: true
  },
  selectAll: {
    type: Boolean,
    required: true
  },
  exportFields: {
    type: Array,
    required: true
  },
  availableFields: {
    type: Array,
    required: true
  },
  includeConceptTags: {
    type: Boolean,
    required: true
  },
  includePersonOrgTags: {
    type: Boolean,
    required: true
  },
  includeTimePlaceTags: {
    type: Boolean,
    required: true
  },

  currentSource: {
    type: String,
    required: true
  }
})

// Emits
const emit = defineEmits([
  'update:newTagMarkEnabled',
  'update:newTagMarkPosition',
  'update:newTagMark',
  'update:metadataSuffixEnabled',
  'update:selectedMetadataFields',
  'update:customSuffixEnabled',
  'update:customSuffix',
  'update:processedTagEnabled',
  'update:processedTag',
  'update:showExportDialog',
  'update:exportOptions',
  'update:selectAll',
  'update:exportFields',
  'update:includeConceptTags',
  'update:includePersonOrgTags',
  'update:includeTimePlaceTags',
  'downloadCSV',
  'openExportDialog',
  'selectAll',
  'export',
  'saveTagsToZotero',
  'getPreviewSuffix'
])

// Computed properties for v-model support
const newTagMarkEnabled = computed({
  get: () => props.newTagMarkEnabled,
  set: (value) => emit('update:newTagMarkEnabled', value)
})

const newTagMarkPosition = computed({
  get: () => props.newTagMarkPosition,
  set: (value) => emit('update:newTagMarkPosition', value)
})

const newTagMark = computed({
  get: () => props.newTagMark,
  set: (value) => emit('update:newTagMark', value)
})

const metadataSuffixEnabled = computed({
  get: () => props.metadataSuffixEnabled,
  set: (value) => emit('update:metadataSuffixEnabled', value)
})

const selectedMetadataFields = computed({
  get: () => props.selectedMetadataFields,
  set: (value) => emit('update:selectedMetadataFields', value)
})

const customSuffixEnabled = computed({
  get: () => props.customSuffixEnabled,
  set: (value) => emit('update:customSuffixEnabled', value)
})

const customSuffix = computed({
  get: () => props.customSuffix,
  set: (value) => emit('update:customSuffix', value)
})

const processedTagEnabled = computed({
  get: () => props.processedTagEnabled,
  set: (value) => emit('update:processedTagEnabled', value)
})

const processedTag = computed({
  get: () => props.processedTag,
  set: (value) => emit('update:processedTag', value)
})

const showExportDialog = computed({
  get: () => props.showExportDialog,
  set: (value) => emit('update:showExportDialog', value)
})

const exportOptions = computed({
  get: () => props.exportOptions,
  set: (value) => emit('update:exportOptions', value)
})

const selectAll = computed({
  get: () => props.selectAll,
  set: (value) => emit('update:selectAll', value)
})

const exportFields = computed({
  get: () => props.exportFields,
  set: (value) => emit('update:exportFields', value)
})

const includeConceptTags = computed({
  get: () => props.includeConceptTags,
  set: (value) => emit('update:includeConceptTags', value)
})

const includePersonOrgTags = computed({
  get: () => props.includePersonOrgTags,
  set: (value) => emit('update:includePersonOrgTags', value)
})

const includeTimePlaceTags = computed({
  get: () => props.includeTimePlaceTags,
  set: (value) => emit('update:includeTimePlaceTags', value)
})

// Helper methods
const getPreviewSuffix = () => {
  let suffix = ''
  
  if (props.metadataSuffixEnabled && props.selectedMetadataFields.length > 0) {
    suffix += `[${props.selectedMetadataFields.join('|')}]` // Example preview
  }
  
  return suffix
}

// Event handlers
const handleDownloadCSV = () => {
  emit('downloadCSV')
}

const handleOpenExportDialog = () => {
  emit('openExportDialog')
}

const handleSelectAll = (val) => {
  emit('selectAll', val)
}

const handleExport = () => {
  emit('export')
}

const handleSaveTagsToZotero = () => {
  emit('saveTagsToZotero')
}
</script>

<style scoped>
.center-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.download-section {
  margin-top: 20px;
  text-align: center;
}

.mark-position-select {
  width: 80px;
  margin: 0 5px;
}

.suffix-input {
  margin-top: 10px;
}

.preview-tag {
  margin: 2px;
}

.button-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
}

.mt-4 {
  margin-top: 20px;
}

.el-input-delimiter {
  width: 60px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
