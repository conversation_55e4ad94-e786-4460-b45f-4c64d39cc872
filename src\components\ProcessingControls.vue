<template>
  <div>
    <el-row justify="center">
      <el-col :span="16" class="center-content">
        <!-- Submit Button -->
        <el-button type="success" :icon="Check" @click="handleSubmitBiblioItems" :loading="isCSVLoading"
          :disabled="isCSVLoading || biblioItems.length === 0">
          {{ isCSVLoading ? `Processing items (${elapsedCSVTime}s)...` : 'Tag Items' }}
        </el-button>
        <el-text>in batch of</el-text>
        <el-input-number v-model="batchSize" :min="1"
          :max="Math.max(biblioItems.length, 1)" :disabled="isCSVLoading || biblioItems.length === 0"
          :placeholder="`${suggestedBatchSize}`" controls-position="right" class="custom-input-number" />
        <!-- Clear All Button -->
        <el-button type="danger" :icon="Delete" @click="handleClearAllItems"
          :disabled="isCSVLoading || biblioItems.length === 0">
          Clear All Items
        </el-button>
      </el-col>
    </el-row>

    <!-- Loading message -->
    <div v-if="isLoading || isCSVLoading" class="loading-message">
      <p>Generating tags... This may take a few minutes.</p>
      <p>Time elapsed: {{ isCSVLoading ? elapsedCSVTime : elapsedTime }} seconds</p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ElRow, ElCol, ElButton, ElText, ElInputNumber } from 'element-plus'
import { Check, Delete } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  biblioItems: {
    type: Array,
    required: true
  },
  isCSVLoading: {
    type: Boolean,
    required: true
  },
  isLoading: {
    type: Boolean,
    required: true
  },
  elapsedCSVTime: {
    type: Number,
    required: true
  },
  elapsedTime: {
    type: Number,
    required: true
  },
  batchSize: {
    type: Number,
    default: null
  },
  suggestedBatchSize: {
    type: Number,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:batchSize', 'submitBiblioItems', 'clearAllItems'])

// Computed properties for v-model support
const batchSize = computed({
  get: () => props.batchSize,
  set: (value) => emit('update:batchSize', value)
})

// Event handlers
const handleSubmitBiblioItems = () => {
  emit('submitBiblioItems')
}

const handleClearAllItems = () => {
  emit('clearAllItems')
}
</script>

<style scoped>
.center-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.custom-input-number {
  width: 120px;
}

.loading-message {
  text-align: center;
  margin: 20px 0;
  padding: 20px;
  background-color: var(--el-bg-color-overlay);
  border-radius: 8px;
}

.loading-message p {
  margin: 5px 0;
  color: var(--el-text-color-primary);
}
</style>
